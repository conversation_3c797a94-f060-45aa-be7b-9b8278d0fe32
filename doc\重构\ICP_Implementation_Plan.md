# 迭代式上下文规划 (ICP) 架构修复与实施方案

## 1. 问题诊断与现状

**核心问题**: 当前的旅行规划功能与产品需求文档 (`TravelPlannerAgent_Refactored_PRD.md`) 和架构设计文档 (`规划距离逻辑.md`) 中定义的 **迭代式上下文规划 (ICP) 架构严重不符**。

**具体表现**:
1.  **功能缺失**: `src/agents/travel_planner_lg/nodes.py` 中的 `run_icp_planning` 函数仅是一个空壳，并未实现文档中描述的以 AI 为核心的“思考 -> 行动 -> 观察”智能循环。
2.  **行程重复**: 由于 ICP 功能缺失导致规划失败，系统回退到使用一套写死的、用于演示的后备（Fallback）数据。该后备数据被不加区分地应用到行程的每一天，造成了前端UI上“每日行程完全重复”的严重问题。
3.  **体验不佳**: 规划结果不具备智能性，无法根据距离、时间、用户偏好进行动态调整，不符合产品设计的核心目标。

## 2. 解决方案：完整实施ICP架构

要从根本上解决问题，必须停止依赖后备数据，转而完整地开发和实施 `规划距离逻辑.md` 第7节中定义的ICP架构。这需要一个系统性的开发计划。

## 3. 实施路线图 (Implementation Roadmap)

以下是具体的、可执行的开发任务清单，旨在将 ICP 架构蓝图转化为实际代码。

### T1: 重构 LangGraph 工作流 (最高优先级)

-   **目标文件**: `src/agents/travel_planner_lg/graph.py`
-   **任务描述**:
    1.  修改 `TravelPlannerGraph.build_graph()` 方法。
    2.  移除当前线性的、名存实亡的 `run_icp_planning` 节点调用。
    3.  建立一个以 `planner_agent_node` 为核心的 **循环计算图**。
    4.  添加一个条件路由（`router`），该路由根据 `planner_agent_node` 的输出指令，决定是调用 `tool_executor_node`（继续循环）还是 `END`（结束规划）。

    ```python
    # 伪代码参考
    workflow.add_node("planner_agent", nodes.planner_agent_node)
    workflow.add_node("tool_executor", nodes.tool_executor_node)
    workflow.add_edge("tool_executor", "planner_agent") # 形成循环
    workflow.add_conditional_edges(
        "planner_agent",
        nodes.route_action, # 新建一个路由函数
        {
            "execute_tool": "tool_executor",
            "finish": END
        }
    )
    ```

### T2: 实现 Planner Agent 核心节点

-   **目标文件**: `src/agents/travel_planner_lg/nodes.py`
-   **任务描述**:
    1.  创建新的核心函数 `async def planner_agent_node(state: StandardAgentState)`。
    2.  **核心逻辑**:
        -   从 `state` 中提取完整的规划上下文（用户意图、已规划行程、剩余时间等）。
        -   从 `UnifiedToolRegistry` 获取所有可用的 `Action Tools` 的 Schema。
        -   使用新的 `planner_agent.md` 提示词模板，结合上下文和可用工具，格式化一个完整的 Prompt。
        -   调用 `ReasoningService` 执行 LLM 推理，让 AI 决定下一步该做什么。
        -   解析 LLM 返回的 JSON 格式行动指令（如 `{ "tool_name": "search_poi", ... }` 或 `{ "tool_name": "finish_planning" }`），并更新到 `state` 中。

### T3: 创建 Planner Agent 提示词

-   **目标文件**: `src/prompts/travel_planner/planner_agent.md` (新建)
-   **任务描述**:
    1.  创建一个强大的 Prompt，这是新架构的“大脑”。
    2.  **核心要点**:
        -   **角色定义**: "你是一位专业的旅行规划师，负责动态、逐步地构建一个完整、合理的多日行程。"
        -   **上下文输入**: 明确告知 AI 它将接收到哪些信息（用户偏好、已规划内容、预算和时间跟踪等）。
        -   **核心任务**: “根据当前状态，决定下一步最合理的操作。”
        -   **时空连续性指令**: **必须**明确指示 AI 维护一个内部的“虚拟时钟”。当安排完一个活动后，需要推进时钟。如果时钟进入饭点，下一步必须是在上一个地点附近寻找餐厅。
        -   **可用工具列表**: 接收 `get_all_action_schemas()` 的结果作为可调用的工具。
        -   **输出格式**: 强制要求以 JSON 格式输出包含 `tool_name` 和 `parameters` 的行动指令。
        -   **终止条件**: 指导 AI 在何时判断规划已完整，并输出 `{ "tool_name": "finish_planning" }` 指令。

### T4: 实现通用工具执行器节点

-   **目标文件**: `src/agents/travel_planner_lg/nodes.py`
-   **任务描述**:
    1.  创建函数 `async def tool_executor_node(state: StandardAgentState)`。
    2.  **核心逻辑**:
        -   从 `state` 中读取 `planner_agent_node` 生成的行动指令。
        -   使用 `unified_registry.execute_action_tool(tool_name, **parameters)` 来动态、统一地执行工具。
        -   将工具执行的结果写回 `state` 中，供下一轮 `planner_agent_node` 思考。

### T5: 完善并注册所有 Action Tools

-   **目标文件**: `src/tools/travel_planner/amap_poi_tools.py` 及其他服务文件。
-   **任务描述**:
    1.  审查所有 `Action Tools`，确保它们功能完善且粒度合适。
    2.  **补全缺失工具**: 根据 `规划距离逻辑.md` 的要求，必须补充 `get_poi_details` 和 `get_poi_images` 等工具，以获取丰富的POI信息。
    3.  **统一注册**: 确保所有需要被 AI 调用的 `Action Tools` 都已通过 `@unified_registry.register_action_tool` 装饰器正确注册。

### T6: 时空一致性验证系统 (关键新增)

-   **目标文件**: `src/agents/travel_planner_lg/validators.py` (新建)
-   **任务描述**:
    1.  **虚拟时钟实现**: 创建 `VirtualClock` 类，精确跟踪每日时间进度，支持活动时长计算和时间推进。
    2.  **地理距离验证**: 实现 `GeographicValidator` 类，验证相邻POI之间的距离是否合理（避免"反复横跳"）。
    3.  **时间冲突检测**: 实现 `TimeConflictDetector` 类，确保活动时间不重叠，用餐时间合理安排。
    4.  **活动可行性验证**: 检查景点开放时间、停车场可用性、充电桩覆盖等实际约束。

    ```python
    # 核心实现示例
    class VirtualClock:
        def __init__(self, start_time="09:00"):
            self.current_time = start_time
            self.daily_schedule = {}
        
        def advance_time(self, duration_hours):
            # 推进虚拟时钟
            pass
        
        def is_meal_time(self):
            # 判断是否到达用餐时间
            return self.is_lunch_time() or self.is_dinner_time()
    ```

### T7: 质量保证和回滚机制

-   **目标文件**: `src/agents/travel_planner_lg/quality_control.py` (新建)
-   **任务描述**:
    1.  **规划质量评分**: 实现多维度质量评分算法（时间合理性、地理连贯性、用户偏好匹配度）。
    2.  **自动回滚机制**: 当检测到质量问题时，能够回滚到上一个稳定状态并重新规划。
    3.  **循环检测**: 防止 Planner Agent 陷入无限循环，设置最大迭代次数和智能终止条件。
    4.  **性能监控**: 跟踪LLM调用次数、工具执行时间，实施成本控制。

### T8: 数据格式标准化与集成

-   **目标文件**: `src/models/itinerary_models.py` (扩展)
-   **任务描述**:
    1.  **EnrichedPOI模型实现**: 根据 `规划距离逻辑.md` 第13节，完整实现 `EnrichedPOI` 数据模型。
    2.  **事件格式验证**: 确保所有SSE事件都符合 `旅游搭子UI.md` 定义的格式规范。
    3.  **现有代码集成**: 复用 `_optimize_daily_route` 等现有的距离优化函数。
    4.  **错误处理标准化**: 统一错误格式和降级策略。

### T9: 测试与验证框架

-   **目标文件**: `tests/integration/test_icp_complete_flow.py` (新建)
-   **任务描述**:
    1.  **端到端测试**: 模拟完整的用户查询到行程生成流程。
    2.  **时空一致性测试**: 验证生成的行程在时间和地理上都是合理的。
    3.  **并发压力测试**: 测试系统在多用户同时使用时的稳定性。
    4.  **用户验收测试**: 设计真实场景的测试用例，确保用户体验符合预期。

## 4. 风险缓解与监控

### 4.1. 关键风险识别

1.  **无限循环风险**: Planner Agent 可能因提示词设计不当而陷入循环。
2.  **成本控制风险**: 大量LLM调用可能导致成本失控。
3.  **性能瓶颈风险**: 复杂的ICP循环可能导致响应时间过长。
4.  **数据一致性风险**: 多个组件间的状态同步可能出现不一致。

### 4.2. 缓解措施

1.  **智能终止机制**: 在 `planner_agent.md` 中明确定义终止条件，并在代码中实施最大迭代限制。
2.  **成本监控**: 实时跟踪LLM调用次数和成本，设置预算阈值。
3.  **性能优化**: 实施工具调用缓存、并行处理等优化措施。
4.  **状态验证**: 在每个关键节点验证状态一致性，及时发现和修复问题。

## 5. 预期效果

完成上述实施路线图后，系统将：
-   **消除行程重复**：AI 会根据每日已规划的内容，智能地规划后续行程。
-   **实现智能规划**：行程将遵循地理距离和时间逻辑，变得连贯、合理。
-   **保证时空一致性**：通过虚拟时钟和地理验证，确保生成的行程在时间和空间上都是可行的。
-   **符合架构设计**：代码将与所有重构文档（`规划距离逻辑.md`, `推送.md`, `TravelPlannerAgent_Refactored_PRD.md`）保持一致。
-   **提升用户体验**：前端将能通过 `PLANNING_LOG` 事件，实时展示 AI 的思考过程，最终呈现一个高质量的、个性化的行程。

## 6. 现有代码分析与修改要求

### 6.1. 现有提示词模板分析 (`src/prompts/travel_planner/04_itinerary_generator.md`)

**现状评估**:
- **优势**: 包含了详细的地理距离优化要求、时空一致性约束、中文输出规范等高质量内容
- **问题**: 采用传统的"一次性生成"模式，与ICP的"迭代式思考"架构根本不兼容
- **修改必要性**: **必须重构**，但应保留其优秀的约束条件和格式要求

**具体修改方案**:
1. **拆分重构**: 将现有提示词拆分为两个新文件：
   - `planner_agent.md`: 支持迭代式思考的AI规划师提示词
   - `itinerary_formatter.md`: 最终JSON格式化输出（保留现有格式要求）

2. **保留精华**: 
   - 地理距离优化要求（30分钟驾驶时间限制、地理聚类原则）
   - 时空一致性约束（虚拟时钟、用餐时间、景点开放时间）
   - 中文输出规范和详细的POI信息要求

3. **增强功能**:
   - 添加"思考->行动->观察"的迭代式工作模式
   - 增加工具调用指导和质量检查机制
   - 实现循环终止条件和错误处理

### 6.2. 现有工作流分析 (`src/agents/travel_planner_lg/graph_v3.py`)

**现状评估**:
- **架构问题**: 虽然命名为"V3"，但实际上是线性的四步流程，缺少真正的ICP循环
- **缺失组件**: 没有planner_agent_node和tool_executor_node的循环结构
- **事件集成**: 有事件总线集成，但缺少详细的规划步骤事件发布

**具体修改要求**:
1. **重构icp_planning节点**: 
   - 移除现有的线性icp_planning节点
   - 添加planner_agent_node和tool_executor_node循环
   - 实现should_continue条件路由

2. **完善事件发布**:
   - 发布PLANNING_LOG事件，展示AI思考过程
   - 发布TOOL_CALL和TOOL_RESULT事件
   - 实现实时状态同步

3. **状态管理增强**:
   - 添加虚拟时钟状态跟踪
   - 实现质量检查和回滚机制
   - 增加循环计数和终止控制

## 7. 与其他重构文档的详细集成规范

### 7.1. 与 `规划距离逻辑.md` 的集成

**核心集成点**：
1.  **复用现有距离优化代码**：
    -   将 `_optimize_daily_route` 函数注册为 Action Tool
    -   在 Planner Agent 中智能调用距离优化
    -   确保 ICP 循环中的每日 POI 都经过地理排序

2.  **EnrichedPOI 模型实现**：
    -   严格按照第13节定义实现 `EnrichedPOI` 类
    -   确保所有 POI 数据包含完整的展示信息
    -   实现从基础 POI 到 EnrichedPOI 的转换流程

3.  **时空连续性逻辑**：
    -   将文档第11.2节的虚拟时钟概念具体实现
    -   确保 Planner Agent 严格遵循时间推进逻辑
    -   实现智能的用餐时间安排

### 7.2. 与 `推送.md` 的集成

**核心集成点**：
1.  **事件驱动架构实现**：
    -   确保 ICP 循环中的每个关键步骤都发布相应事件
    -   实现 Redis 状态与 LangGraph 状态的实时同步
    -   保证事件发布的时序正确性

2.  **统一事件格式**：
    -   严格遵循推送文档第四章定义的 SSE 事件格式
    -   确保所有事件都包含必要的 timestamp 字段
    -   实现事件格式的自动验证

3.  **性能优化**：
    -   控制事件发布频率，避免前端过载
    -   实现事件批量处理和缓存机制
    -   确保高并发场景下的稳定性

### 7.3. 与 `旅游搭子UI.md` 的集成

**核心集成点**：
1.  **前后端数据格式一致性**：
    -   确保后端生成的 POI 数据完全符合前端组件的预期格式
    -   实现 StandardAgentState 字段与前端状态的直接映射
    -   保证所有 SSE 事件都能被前端正确解析和展示

2.  **用户体验优化**：
    -   控制 ICP 迭代的执行节奏，确保前端有足够时间渲染
    -   实现智能的进度反馈和状态更新
    -   确保错误处理的用户友好性

3.  **实时性保证**：
    -   优化事件推送延迟，实现真正的实时体验
    -   确保行程卡片的动态添加效果流畅
    -   实现前端状态的自动恢复和同步

## 7. 实施优先级调整

基于时空一致性的关键要求，建议调整实施优先级：

**第一阶段（基础架构）**：
-   T4: 工具执行器节点（提前实施，为后续提供基础）
-   T5: Action Tools 完善（确保工具链完整）
-   T8: 数据格式标准化（建立统一的数据基础）

**第二阶段（核心功能）**：
-   T1: LangGraph 工作流重构
-   T2: Planner Agent 核心节点
-   T6: 时空一致性验证系统（关键新增）

**第三阶段（质量保证）**：
-   T3: Planner Agent 提示词（精心设计，避免无限循环）
-   T7: 质量保证和回滚机制
-   T9: 测试与验证框架

这样的优先级安排确保了系统的稳定性和时空一致性要求能够得到充分保障。

## 8. 提示词模板重构示例

### 8.1. 新的 Planner Agent 提示词模板

基于对现有 `04_itinerary_generator.md` 的分析，以下是重构后的 `planner_agent.md` 示例：

```markdown
# AI旅行规划师 (Planner Agent) - 迭代式上下文规划

你是一位专业的AI旅行规划师，采用"思考->行动->观察"的迭代式规划方法，逐步构建完整、合理的多日行程。

## 核心工作模式

### 思考阶段 (Think)
- 分析当前规划状态和已有信息
- 识别下一步需要解决的问题
- 制定具体的行动计划

### 行动阶段 (Act)  
- 调用合适的工具获取信息
- 执行具体的规划任务
- 更新规划状态

### 观察阶段 (Observe)
- 评估行动结果的质量
- 检查是否满足时空一致性要求
- 决定是否需要继续迭代

## 地理距离优化要求 (继承自04_itinerary_generator.md)

### 1. 地理区域分离原则
- 对于包含特殊地理区域（如岛屿、远郊山区）的行程，应将这些区域的景点集中安排
- 避免与主城区景点频繁切换
- 岛屿行程需考虑轮渡/航班时间和班次限制

### 2. 驾驶时间限制
- 同一天内相邻景点的驾驶时间不超过30-45分钟
- 如果某景点距离其他景点超过1.5小时车程，应考虑单独安排

### 3. 餐厅就近选择
- 午餐和晚餐地点必须选择距离当前景点最近的餐厅
- 避免为了用餐而进行长距离的折返移动

## 虚拟时钟管理

维护内部时钟状态，确保：
- 活动时间的合理推进
- 用餐时间的准确安排（11:30-13:30午餐，17:30-19:30晚餐）
- 景点开放时间的遵守
- 交通和休息时间的预留

## 可用工具列表

{{ available_tools | tojson(indent=2) }}

## 输出格式

必须以JSON格式输出行动指令：

{
  "thought": "当前的思考过程和分析",
  "tool_name": "要调用的工具名称",
  "parameters": {
    "param1": "参数值1",
    "param2": "参数值2"
  },
  "reason": "选择此工具的原因"
}

## 终止条件

当满足以下条件时，输出 {"tool_name": "finish_planning"}:
- 所有天数的行程都已完成规划
- 时空一致性验证通过
- 用户偏好匹配度达标
- 预算控制在合理范围内
```

### 8.2. 格式化器提示词模板

保留现有 `04_itinerary_generator.md` 中的JSON格式要求：

```markdown
# 行程格式化器 (Itinerary Formatter)

将AI规划师的规划结果格式化为标准JSON输出。

## 输出要求

[完整保留04_itinerary_generator.md中的所有JSON格式要求]
[完整保留中文输出规范和详细的POI信息要求]
```

这样的重构既保留了现有提示词的优秀内容，又实现了对ICP架构的完整支持。 