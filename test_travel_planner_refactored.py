#!/usr/bin/env python3
"""
旅游规划系统重构后的综合测试脚本

测试内容：
1. POI搜索重试逻辑
2. POI去重功能
3. 两阶段自动化工作流
4. V3 API集成
5. 真实数据调用验证

严禁使用模拟数据，必须使用真实数据进行测试
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

from src.tools.travel_planner.amap_poi_tools import search_poi
from src.tools.travel_planner.icp_tools import (
    generate_planning_thought,
    select_next_action,
    observe_action_result,
    update_planning_state,
    check_planning_completion
)
from src.agents.travel_planner_lg.graph_v3 import TravelPlannerGraphV3
from src.services.unified_event_bus import UnifiedEventBus
from src.database.redis_client import get_redis_client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TravelPlannerRefactoredTest:
    """旅游规划系统重构测试类"""
    
    def __init__(self):
        self.test_results = {}
        self.redis_client = None
        self.event_bus = None
        
    async def setup(self):
        """测试环境初始化"""
        logger.info("=== 初始化测试环境 ===")
        try:
            # 初始化Redis客户端
            self.redis_client = await get_redis_client()
            self.event_bus = UnifiedEventBus(self.redis_client)
            logger.info("✅ Redis客户端和事件总线初始化成功")
            return True
        except Exception as e:
            logger.error(f"❌ 测试环境初始化失败: {str(e)}")
            return False
    
    async def test_poi_search_retry_logic(self):
        """测试POI搜索重试逻辑"""
        logger.info("\n=== 测试POI搜索重试逻辑 ===")
        
        try:
            # 测试正常搜索
            results = await search_poi(
                keywords="景点",
                city="上海",
                types="ATTRACTION",
                page_size=5
            )
            
            if isinstance(results, list):
                if len(results) > 0:
                    logger.info(f"✅ POI搜索成功，返回{len(results)}个结果")
                    # 验证结果格式
                    first_poi = results[0]
                    required_fields = ['name', 'address', 'poi_type']
                    missing_fields = [field for field in required_fields if field not in first_poi]
                    
                    if not missing_fields:
                        logger.info("✅ POI数据格式验证通过")
                        self.test_results['poi_search_retry'] = True
                        return True
                    else:
                        logger.error(f"❌ POI数据缺少必需字段: {missing_fields}")
                else:
                    logger.warning("⚠️ POI搜索返回空结果，可能是API配置问题")
                    self.test_results['poi_search_retry'] = False
                    return False
            else:
                logger.error(f"❌ POI搜索返回类型错误: {type(results)}")
                self.test_results['poi_search_retry'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ POI搜索测试失败: {str(e)}")
            self.test_results['poi_search_retry'] = False
            return False
    
    async def test_poi_deduplication(self):
        """测试POI去重功能"""
        logger.info("\n=== 测试POI去重功能 ===")
        
        try:
            # 模拟ICP状态
            test_state = {
                "daily_plans": {
                    "day1": [
                        {
                            "poi_id": "existing_001",
                            "name": "外滩",
                            "address": "上海市黄浦区中山东一路",
                            "poi_type": "ATTRACTION"
                        }
                    ]
                },
                "current_iteration": 1,
                "max_iterations": 3
            }
            
            # 模拟添加重复POI的操作
            action = {
                "tool_name": "search_poi",
                "target_day": "day1",
                "keywords": "外滩"
            }
            
            # 模拟搜索结果（包含重复的外滩）
            action_result = [
                {
                    "poi_id": "new_001",
                    "name": "外滩",  # 重复的POI
                    "address": "上海市黄浦区中山东一路",
                    "poi_type": "ATTRACTION"
                },
                {
                    "poi_id": "new_002", 
                    "name": "东方明珠塔",  # 新的POI
                    "address": "上海市浦东新区世纪大道1号",
                    "poi_type": "ATTRACTION"
                }
            ]
            
            observation = {"success": True, "message": "搜索成功"}
            
            # 调用更新状态函数（非async）
            updated_state = update_planning_state(
                test_state, action, action_result, observation
            )
            
            # 验证去重效果
            day1_pois = updated_state.get("daily_plans", {}).get("day1", [])
            poi_names = [poi.get("name") for poi in day1_pois]
            
            # 检查是否有重复
            unique_names = set(poi_names)
            if len(poi_names) == len(unique_names):
                logger.info(f"✅ POI去重测试通过，当前POI: {poi_names}")
                self.test_results['poi_deduplication'] = True
                return True
            else:
                logger.error(f"❌ POI去重失败，发现重复: {poi_names}")
                self.test_results['poi_deduplication'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ POI去重测试失败: {str(e)}")
            self.test_results['poi_deduplication'] = False
            return False
    
    async def test_two_stage_workflow(self):
        """测试两阶段自动化工作流"""
        logger.info("\n=== 测试两阶段自动化工作流 ===")
        
        try:
            # 准备测试输入
            input_data = {
                "user_query": "我想去上海旅游3天，喜欢历史文化景点",
                "user_id": 1,  # 使用真实的MySQL用户ID
                "execution_mode": "auto",
                "task_id": f"test_task_{int(datetime.utcnow().timestamp() * 1000)}"
            }
            
            # 创建图实例
            graph = TravelPlannerGraphV3(event_bus=self.event_bus)
            
            # 执行工作流（流式）
            logger.info("开始执行两阶段工作流...")
            step_count = 0
            final_result = None
            
            async for step in graph.stream(input_data):
                step_count += 1
                logger.info(f"步骤 {step_count}: {list(step.keys())}")
                
                # 获取最后一步的结果
                for node_name, node_result in step.items():
                    if isinstance(node_result, dict):
                        final_result = node_result
                        
                        # 检查是否完成
                        if node_result.get("is_completed", False):
                            logger.info("✅ 工作流执行完成")
                            break
            
            # 验证结果
            if final_result and final_result.get("is_completed", False):
                # 检查是否有最终行程
                final_itinerary = final_result.get("final_itinerary", {})
                if final_itinerary:
                    logger.info("✅ 两阶段工作流测试通过")
                    logger.info(f"生成的行程天数: {len(final_itinerary.get('daily_plans', {}))}")
                    self.test_results['two_stage_workflow'] = True
                    return True
                else:
                    logger.error("❌ 工作流完成但未生成最终行程")
                    self.test_results['two_stage_workflow'] = False
                    return False
            else:
                logger.error("❌ 工作流未正常完成")
                self.test_results['two_stage_workflow'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ 两阶段工作流测试失败: {str(e)}")
            self.test_results['two_stage_workflow'] = False
            return False
    
    async def test_real_data_integration(self):
        """测试真实数据集成"""
        logger.info("\n=== 测试真实数据集成 ===")
        
        try:
            # 测试MySQL数据库连接
            from src.database.database import get_database
            db = await get_database()
            
            # 查询用户数据
            user_query = "SELECT * FROM users WHERE id = 1 LIMIT 1"
            user_result = await db.fetch_one(user_query)
            
            if user_result:
                logger.info(f"✅ MySQL数据库连接成功，用户ID 1存在")
                logger.info(f"用户信息: {dict(user_result)}")
            else:
                logger.error("❌ 用户ID 1不存在于数据库中")
                self.test_results['real_data_integration'] = False
                return False
            
            # 测试POI API（真实调用）
            poi_results = await search_poi(
                keywords="博物馆",
                city="上海", 
                types="ATTRACTION",
                page_size=3
            )
            
            if poi_results and len(poi_results) > 0:
                logger.info(f"✅ POI API调用成功，返回{len(poi_results)}个结果")
                # 验证不是后备数据
                first_poi = poi_results[0]
                if first_poi.get("poi_id", "").startswith("fallback_"):
                    logger.error("❌ 检测到使用了后备数据，违反真实数据要求")
                    self.test_results['real_data_integration'] = False
                    return False
                else:
                    logger.info("✅ 确认使用真实POI数据")
            else:
                logger.error("❌ POI API调用失败或返回空结果")
                self.test_results['real_data_integration'] = False
                return False
            
            self.test_results['real_data_integration'] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ 真实数据集成测试失败: {str(e)}")
            self.test_results['real_data_integration'] = False
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始旅游规划系统重构测试")
        
        # 初始化
        if not await self.setup():
            return False
        
        # 运行各项测试
        tests = [
            ("POI搜索重试逻辑", self.test_poi_search_retry_logic),
            ("POI去重功能", self.test_poi_deduplication),
            ("真实数据集成", self.test_real_data_integration),
            ("两阶段自动化工作流", self.test_two_stage_workflow),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                if await test_func():
                    passed_tests += 1
                    logger.info(f"✅ {test_name} - 通过")
                else:
                    logger.error(f"❌ {test_name} - 失败")
            except Exception as e:
                logger.error(f"❌ {test_name} - 异常: {str(e)}")
        
        # 输出测试总结
        logger.info(f"\n=== 测试总结 ===")
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过测试: {passed_tests}")
        logger.info(f"失败测试: {total_tests - passed_tests}")
        logger.info(f"通过率: {passed_tests/total_tests*100:.1f}%")
        
        if passed_tests == total_tests:
            logger.info("🎉 所有测试通过！系统重构成功")
            return True
        else:
            logger.error("❌ 部分测试失败，需要进一步修复")
            return False


async def main():
    """主函数"""
    tester = TravelPlannerRefactoredTest()
    success = await tester.run_all_tests()
    
    if success:
        print("\n✅ 重构测试完成，系统准备就绪")
        return 0
    else:
        print("\n❌ 重构测试失败，需要修复问题")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
