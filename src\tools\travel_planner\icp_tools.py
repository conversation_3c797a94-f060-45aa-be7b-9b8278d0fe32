"""
ICP (迭代式上下文规划) 工具集 (V3.0 - 统一架构版)

实现"思考-行动-观察"循环所需的Planner Tools，
支持AI驱动的迭代式旅行规划。
"""

import json
import logging
import math
from typing import Dict, Any, List, Optional
from datetime import datetime
from src.tools.unified_registry import unified_registry
from src.models.poi import EnrichedPOI, BasicPOI, POIType, convert_basic_to_enriched

logger = logging.getLogger(__name__)


def _generate_suggested_time(poi_type: str) -> str:
    """
    根据POI类型生成建议游览时间

    Args:
        poi_type: POI类型

    Returns:
        建议时间字符串
    """
    time_mapping = {
        "ATTRACTION": "上午 09:00 - 12:00",
        "RESTAURANT": "午餐 12:00 - 13:30",
        "HOTEL": "入住 15:00",
        "SHOPPING": "下午 14:00 - 17:00",
        "OTHER": "上午 10:00 - 11:30"
    }
    return time_mapping.get(poi_type, "上午 10:00 - 11:30")


@unified_registry.register_planner_tool
def generate_planning_thought(
    current_state: Dict[str, Any], 
    planning_context: Dict[str, Any],
    step_number: int
) -> Dict[str, Any]:
    """
    生成规划思考
    
    基于当前状态和规划上下文，生成下一步的思考内容
    
    Args:
        current_state: 当前规划状态
        planning_context: 规划上下文
        step_number: 当前步骤编号
    
    Returns:
        思考结果
    """
    try:
        # 分析当前进度
        daily_plans = current_state.get("daily_plans", {})
        completed_days = len([day for day, plans in daily_plans.items() if plans])
        total_days = planning_context.get("constraints", {}).get("max_days", 3)
        
        # 分析预算使用情况
        budget_used = current_state.get("total_budget_tracker", 0)
        budget_limit = planning_context.get("constraints", {}).get("budget_limit", 1000)
        
        # 生成思考内容
        if completed_days == 0:
            thought = f"开始第{step_number}步规划。需要为{total_days}天行程制定计划。首先分析目的地和用户偏好，确定第一天的核心景点。"
        elif completed_days < total_days:
            current_day = completed_days + 1
            thought = f"第{step_number}步：继续规划第{current_day}天行程。已完成{completed_days}天规划，预算已用{budget_used}元。需要考虑与前几天的连贯性和交通便利性。"
        else:
            thought = f"第{step_number}步：所有天数规划已完成，进行最终检查和优化。总预算使用{budget_used}元，需要验证行程的合理性和完整性。"
        
        # 确定下一步行动
        if completed_days < total_days:
            next_action_type = "search_poi"
            action_reason = f"需要为第{completed_days + 1}天搜索合适的景点"
        elif not current_state.get("accommodation_planned", False):
            next_action_type = "search_accommodation"
            action_reason = "需要安排住宿"
        else:
            next_action_type = "finalize_itinerary"
            action_reason = "完成最终行程整理"
        
        return {
            "thought_content": thought,
            "reasoning_step": step_number,
            "progress_analysis": {
                "completed_days": completed_days,
                "total_days": total_days,
                "budget_used": budget_used,
                "budget_remaining": budget_limit - budget_used
            },
            "next_action_suggestion": {
                "action_type": next_action_type,
                "reason": action_reason
            },
            "confidence": 0.8
        }
        
    except Exception as e:
        logger.error(f"Failed to generate planning thought: {str(e)}")
        return {
            "thought_content": f"规划思考生成失败: {str(e)}",
            "reasoning_step": step_number,
            "confidence": 0.0,
            "error": str(e)
        }


@unified_registry.register_planner_tool
def select_next_action(
    thought_result: Dict[str, Any],
    available_tools: List[str],
    current_state: Dict[str, Any]
) -> Dict[str, Any]:
    """
    选择下一步行动
    
    基于思考结果和可用工具，选择最合适的下一步行动
    
    Args:
        thought_result: 思考结果
        available_tools: 可用工具列表
        current_state: 当前状态
    
    Returns:
        行动选择结果
    """
    try:
        suggested_action = thought_result.get("next_action_suggestion", {})
        action_type = suggested_action.get("action_type", "search_poi")
        
        # 根据行动类型选择具体工具和参数
        if action_type == "search_poi":
            # 确定搜索参数
            daily_plans = current_state.get("daily_plans", {})
            current_day = len([day for day, plans in daily_plans.items() if plans]) + 1
            
            # 从consolidated_intent获取目的地信息
            consolidated_intent = current_state.get("consolidated_intent", {})
            destinations = consolidated_intent.get("destinations", ["北京"])
            current_destination = destinations[0] if destinations else "北京"
            
            # 从偏好分析获取景点类型
            preferences = consolidated_intent.get("preferences", {})
            attraction_prefs = preferences.get("attractions", {})
            preferred_types = attraction_prefs.get("preferred_types", ["历史文化"])
            
            action = {
                "tool_name": "search_poi",
                "parameters": {
                    "keywords": preferred_types[0] if preferred_types else "景点",
                    "city": current_destination,
                    "types": "景点",
                    "page_size": 10
                },
                "target_day": current_day,
                "expected_result": f"为第{current_day}天获取{preferred_types[0] if preferred_types else '景点'}列表"
            }
            
        elif action_type == "search_accommodation":
            consolidated_intent = current_state.get("consolidated_intent", {})
            destinations = consolidated_intent.get("destinations", ["北京"])
            
            action = {
                "tool_name": "search_poi",
                "parameters": {
                    "keywords": "酒店",
                    "city": destinations[0] if destinations else "北京",
                    "types": "住宿",
                    "page_size": 5
                },
                "expected_result": "获取住宿选项列表"
            }
            
        elif action_type == "finalize_itinerary":
            action = {
                "tool_name": "format_final_itinerary",
                "parameters": {
                    "daily_plans": current_state.get("daily_plans", {}),
                    "consolidated_intent": current_state.get("consolidated_intent", {})
                },
                "expected_result": "生成最终完整行程"
            }
            
        else:
            # 默认行动
            action = {
                "tool_name": "search_poi",
                "parameters": {
                    "keywords": "景点",
                    "city": "北京",
                    "page_size": 5
                },
                "expected_result": "获取景点信息"
            }
        
        return {
            "selected_action": action,
            "action_reasoning": suggested_action.get("reason", "基于当前状态选择的行动"),
            "confidence": thought_result.get("confidence", 0.8),
            "alternatives": []  # 可以添加备选行动
        }
        
    except Exception as e:
        logger.error(f"Failed to select next action: {str(e)}")
        return {
            "selected_action": {
                "tool_name": "search_poi",
                "parameters": {"keywords": "景点", "city": "北京"},
                "expected_result": "获取基础景点信息"
            },
            "action_reasoning": f"行动选择失败，使用默认行动: {str(e)}",
            "confidence": 0.1,
            "error": str(e)
        }


@unified_registry.register_planner_tool
def observe_action_result(
    action: Dict[str, Any],
    action_result: Any,
    current_state: Dict[str, Any]
) -> Dict[str, Any]:
    """
    观察行动结果
    
    分析工具执行结果，评估是否达到预期目标
    
    Args:
        action: 执行的行动
        action_result: 行动执行结果
        current_state: 当前状态
    
    Returns:
        观察结果
    """
    try:
        tool_name = action.get("tool_name", "unknown")
        expected_result = action.get("expected_result", "")
        
        # 分析结果质量
        if action_result is None:
            observation = {
                "success": False,
                "quality_score": 0.0,
                "observation": "工具执行失败，未获得结果",
                "next_step_suggestion": "重试或选择其他工具"
            }
        elif isinstance(action_result, list) and len(action_result) == 0:
            observation = {
                "success": False,
                "quality_score": 0.2,
                "observation": "工具执行成功但未找到相关结果",
                "next_step_suggestion": "调整搜索参数或选择其他工具"
            }
        elif isinstance(action_result, list) and len(action_result) > 0:
            # 分析POI搜索结果
            quality_score = min(1.0, len(action_result) / 5.0)  # 5个结果为满分
            
            observation = {
                "success": True,
                "quality_score": quality_score,
                "observation": f"成功获得{len(action_result)}个结果，质量评分{quality_score:.2f}",
                "result_summary": {
                    "count": len(action_result),
                    "sample_items": [item.get("name", "未知") for item in action_result[:3]]
                },
                "next_step_suggestion": "将结果添加到行程规划中" if quality_score > 0.6 else "考虑调整搜索条件"
            }
        else:
            # 其他类型结果
            observation = {
                "success": True,
                "quality_score": 0.7,
                "observation": f"获得{tool_name}执行结果",
                "result_summary": str(action_result)[:200],
                "next_step_suggestion": "继续下一步规划"
            }
        
        # 评估是否需要继续规划
        daily_plans = current_state.get("daily_plans", {})
        total_days = current_state.get("consolidated_intent", {}).get("travel_days", 3)
        completed_days = len([day for day, plans in daily_plans.items() if plans])
        
        should_continue = completed_days < total_days or not observation["success"]
        
        observation.update({
            "should_continue_planning": should_continue,
            "planning_progress": {
                "completed_days": completed_days,
                "total_days": total_days,
                "completion_rate": completed_days / total_days if total_days > 0 else 0
            }
        })
        
        return observation
        
    except Exception as e:
        logger.error(f"Failed to observe action result: {str(e)}")
        return {
            "success": False,
            "quality_score": 0.0,
            "observation": f"结果观察失败: {str(e)}",
            "should_continue_planning": True,
            "error": str(e)
        }


@unified_registry.register_planner_tool
def update_planning_state(
    current_state: Dict[str, Any],
    action: Dict[str, Any],
    action_result: Any,
    observation: Dict[str, Any]
) -> Dict[str, Any]:
    """
    更新规划状态
    
    基于行动结果和观察，更新规划状态
    
    Args:
        current_state: 当前状态
        action: 执行的行动
        action_result: 行动结果
        observation: 观察结果
    
    Returns:
        更新后的状态
    """
    try:
        updated_state = current_state.copy()
        
        # 更新工具结果缓存
        tool_results = updated_state.get("tool_results", {})
        tool_name = action.get("tool_name", "unknown")
        tool_results[tool_name] = action_result
        updated_state["tool_results"] = tool_results
        
        # 如果是POI搜索且成功，更新daily_plans（带去重逻辑）
        if tool_name == "search_poi" and observation.get("success", False):
            target_day = action.get("target_day")
            if target_day and isinstance(action_result, list):
                daily_plans = updated_state.get("daily_plans", {})

                # 获取当前已有的POI列表
                existing_pois = daily_plans.get(target_day, [])
                existing_poi_names = {poi.get("name", "").lower() for poi in existing_pois}

                # 选择最佳POI添加到行程，避免重复
                new_pois = []
                for poi in action_result[:5]:  # 从前5个中选择
                    poi_name = poi.get("name", "未知景点")
                    poi_name_lower = poi_name.lower()

                    # 检查是否已存在相同名称的POI
                    if poi_name_lower not in existing_poi_names:
                        new_poi = {
                            "poi_id": poi.get("poi_id", f"poi_{len(existing_pois) + len(new_pois)}"),
                            "name": poi_name,
                            "address": poi.get("address", ""),
                            "poi_type": poi.get("poi_type", "ATTRACTION"),
                            "location": poi.get("location", ""),
                            "rating": poi.get("rating", 0),
                            "phone_number": poi.get("phone", ""),
                            "introduction": poi.get("description", f"{poi_name}是一个值得游览的地方"),
                            "suggested_time": _generate_suggested_time(poi.get("poi_type", "ATTRACTION")),
                            "image_urls": poi.get("images", [])
                        }
                        new_pois.append(new_poi)
                        existing_poi_names.add(poi_name_lower)

                        # 限制每天最多3个新POI
                        if len(new_pois) >= 3:
                            break
                    else:
                        logger.info(f"跳过重复POI: {poi_name}")

                # 合并现有POI和新POI
                if new_pois:
                    daily_plans[target_day] = existing_pois + new_pois
                    updated_state["daily_plans"] = daily_plans
                    logger.info(f"为第{target_day}天添加了{len(new_pois)}个新POI，总计{len(daily_plans[target_day])}个POI")
                else:
                    logger.warning(f"第{target_day}天没有找到新的POI，可能都已存在")
        
        # 更新规划日志
        planning_log = updated_state.get("planning_log", [])
        log_entry = f"执行{tool_name}，结果：{observation.get('observation', '未知')}"
        planning_log.append(log_entry)
        updated_state["planning_log"] = planning_log
        
        # 更新当前行动
        updated_state["current_action"] = action
        
        return updated_state
        
    except Exception as e:
        logger.error(f"Failed to update planning state: {str(e)}")
        return current_state


@unified_registry.register_planner_tool
def check_planning_completion(
    current_state: Dict[str, Any],
    planning_context: Dict[str, Any]
) -> Dict[str, Any]:
    """
    检查规划完成情况
    
    评估当前规划是否已完成或需要继续
    
    Args:
        current_state: 当前状态
        planning_context: 规划上下文
    
    Returns:
        完成情况检查结果
    """
    try:
        daily_plans = current_state.get("daily_plans", {})
        total_days = planning_context.get("constraints", {}).get("max_days", 3)
        
        # 检查每天是否都有规划
        completed_days = 0
        for day in range(1, total_days + 1):
            if day in daily_plans and daily_plans[day]:
                completed_days += 1
        
        completion_rate = completed_days / total_days if total_days > 0 else 0
        is_complete = completion_rate >= 1.0
        
        # 检查规划质量
        quality_issues = []
        if completed_days < total_days:
            quality_issues.append(f"还有{total_days - completed_days}天未规划")
        
        # 检查预算
        budget_used = current_state.get("total_budget_tracker", 0)
        budget_limit = planning_context.get("constraints", {}).get("budget_limit", 1000)
        if budget_used > budget_limit:
            quality_issues.append(f"预算超支{budget_used - budget_limit}元")
        
        return {
            "is_complete": is_complete,
            "completion_rate": completion_rate,
            "completed_days": completed_days,
            "total_days": total_days,
            "quality_score": 1.0 - len(quality_issues) * 0.2,
            "quality_issues": quality_issues,
            "recommendation": "规划完成" if is_complete and not quality_issues else "需要继续规划或优化"
        }
        
    except Exception as e:
        logger.error(f"Failed to check planning completion: {str(e)}")
        return {
            "is_complete": False,
            "completion_rate": 0.0,
            "quality_score": 0.0,
            "error": str(e),
            "recommendation": "检查失败，建议重新评估"
        }


@unified_registry.register_action_tool
async def optimize_daily_route(pois: List[Dict[str, Any]], trace_id: str = "") -> List[Dict[str, Any]]:
    """
    优化当天的POI访问顺序，基于地理位置进行排序以减少路线上的"反复横跳"

    Args:
        pois: POI列表，每个POI应包含location字段（格式为"lng,lat"）
        trace_id: 追踪ID

    Returns:
        优化后的POI列表
    """
    try:
        logger.info(f"[{trace_id}] 开始优化每日路线，POI数量: {len(pois)}")

        if len(pois) <= 1:
            logger.info(f"[{trace_id}] POI数量不足，无需优化")
            return pois

        # 提取有效位置的POI
        pois_with_location = []
        pois_without_location = []

        for poi in pois:
            location = poi.get('location', '').strip()
            if location and ',' in location:
                try:
                    lng_str, lat_str = location.split(',', 1)
                    lng, lat = float(lng_str.strip()), float(lat_str.strip())

                    # 验证经纬度范围
                    if -180 <= lng <= 180 and -90 <= lat <= 90:
                        poi_copy = poi.copy()
                        poi_copy['_lng'] = lng
                        poi_copy['_lat'] = lat
                        pois_with_location.append(poi_copy)
                    else:
                        logger.warning(f"[{trace_id}] POI {poi.get('name', 'Unknown')} 经纬度超出范围: {location}")
                        pois_without_location.append(poi)
                except (ValueError, IndexError) as e:
                    logger.warning(f"[{trace_id}] POI {poi.get('name', 'Unknown')} 位置解析失败: {location}, 错误: {str(e)}")
                    pois_without_location.append(poi)
            else:
                logger.warning(f"[{trace_id}] POI {poi.get('name', 'Unknown')} 缺少有效位置信息")
                pois_without_location.append(poi)

        if not pois_with_location:
            logger.warning(f"[{trace_id}] 没有有效位置的POI，返回原始顺序")
            return pois

        # 实现简单的地理排序算法
        # 按经度排序，然后按纬度排序，实现从西到东、从南到北的游览顺序
        pois_with_location.sort(key=lambda x: (x.get('_lng', 0), x.get('_lat', 0)))

        # 移除临时添加的经纬度字段
        for poi in pois_with_location:
            poi.pop('_lng', None)
            poi.pop('_lat', None)

        # 将有位置的POI和无位置的POI合并（无位置的放在最后）
        optimized_pois = pois_with_location + pois_without_location

        logger.info(f"[{trace_id}] 路线优化完成，有效位置POI: {len(pois_with_location)}, 无位置POI: {len(pois_without_location)}")

        return optimized_pois

    except Exception as e:
        logger.error(f"[{trace_id}] 路线优化失败: {str(e)}")
        return pois  # 返回原始顺序


@unified_registry.register_action_tool
async def calculate_route_distance(pois: List[Dict[str, Any]], trace_id: str = "") -> Dict[str, Any]:
    """
    计算POI列表的总路线距离和时间

    Args:
        pois: POI列表
        trace_id: 追踪ID

    Returns:
        路线统计信息
    """
    try:
        logger.info(f"[{trace_id}] 开始计算路线距离，POI数量: {len(pois)}")

        if len(pois) < 2:
            return {
                "total_distance": 0.0,
                "total_duration": 0,
                "route_segments": [],
                "average_distance": 0.0
            }

        total_distance = 0.0  # 公里
        total_duration = 0    # 分钟
        route_segments = []

        for i in range(len(pois) - 1):
            current_poi = pois[i]
            next_poi = pois[i + 1]

            current_location = current_poi.get('location', '')
            next_location = next_poi.get('location', '')

            if current_location and next_location:
                # 计算直线距离（简化版本）
                distance = _calculate_haversine_distance(current_location, next_location)
                # 估算步行时间（假设步行速度4km/h）
                duration = int(distance * 15)  # 分钟

                segment = {
                    "from": current_poi.get('name', 'Unknown'),
                    "to": next_poi.get('name', 'Unknown'),
                    "distance": round(distance, 2),
                    "duration": duration
                }
                route_segments.append(segment)

                total_distance += distance
                total_duration += duration

        average_distance = total_distance / len(route_segments) if route_segments else 0.0

        result = {
            "total_distance": round(total_distance, 2),
            "total_duration": total_duration,
            "route_segments": route_segments,
            "average_distance": round(average_distance, 2)
        }

        logger.info(f"[{trace_id}] 路线计算完成，总距离: {result['total_distance']}km, 总时间: {result['total_duration']}分钟")

        return result

    except Exception as e:
        logger.error(f"[{trace_id}] 路线距离计算失败: {str(e)}")
        return {
            "total_distance": 0.0,
            "total_duration": 0,
            "route_segments": [],
            "average_distance": 0.0
        }


def _calculate_haversine_distance(location1: str, location2: str) -> float:
    """
    使用Haversine公式计算两个经纬度点之间的直线距离

    Args:
        location1: 位置1，格式为"lng,lat"
        location2: 位置2，格式为"lng,lat"

    Returns:
        距离（公里）
    """
    try:
        lng1, lat1 = map(float, location1.split(','))
        lng2, lat2 = map(float, location2.split(','))

        # 转换为弧度
        lat1, lng1, lat2, lng2 = map(math.radians, [lat1, lng1, lat2, lng2])

        # Haversine公式
        dlat = lat2 - lat1
        dlng = lng2 - lng1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlng/2)**2
        c = 2 * math.asin(math.sqrt(a))

        # 地球半径（公里）
        r = 6371

        return c * r

    except (ValueError, IndexError):
        return 0.0
